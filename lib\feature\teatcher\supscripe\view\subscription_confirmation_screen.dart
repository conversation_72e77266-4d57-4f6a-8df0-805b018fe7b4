import 'package:al_7esa/core/widget/appbar_teatcher/appbar_teatcher_subscription_confirmation.dart';
import 'package:flutter/material.dart';
import 'package:al_7esa/feature/teatcher/supscripe/view/widgets/subscription_confirmation_body.dart';

class SubscriptionConfirmationScreen extends StatelessWidget {
  final String planTitle;
  final String price;
  final String currency;
  final String duration;
  final List<String> features;
  final List<Color> gradientColors;

  const SubscriptionConfirmationScreen({
    super.key,
    required this.planTitle,
    required this.price,
    required this.currency,
    required this.duration,
    required this.features,
    required this.gradientColors,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: appBarTeatcherSubscriptionConfirmation(),
      body: SubscriptionConfirmationBody(
        planTitle: planTitle,
        price: price,
        currency: currency,
        duration: duration,
        features: features,
        gradientColors: gradientColors,
      ),
    );
  }
}
