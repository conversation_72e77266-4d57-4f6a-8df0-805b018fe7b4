import 'package:al_7esa/core/my_text_field.dart';
import 'package:al_7esa/core/spacing.dart';
import 'package:al_7esa/feature/teatcher/notification/view/widgets/notification_item_fixed.dart';
import 'package:al_7esa/feature/teatcher/notification/view/widgets/notification_item_not_fixed.dart';
import 'package:flutter/material.dart';

class NotificationBody extends StatefulWidget {
  const NotificationBody({super.key});

  @override
  State<NotificationBody> createState() => _NotificationBodyState();
}

class _NotificationBodyState extends State<NotificationBody> {
  String selectedFilter = 'الكل';
  String selectedStatus = 'مقروءة';
  TextEditingController searchController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        vSpace(20),
        // Statistics Section
        Container(
          color: Colors.grey[50],
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildStatItem('1', 'مقروءة', Colors.green),
              _buildStatItem('0', 'غير مقروءة', Colors.orange),
              _buildStatItem('1', 'المجموع', Colors.blue),
            ],
          ),
        ),

        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
          child: CustomTextField(
            onTap: () {},
            validator: (val) {
              if (val == null || val.isEmpty) {
                return 'يرجى إدخال نص للبحث';
              }
              return null;
            },
            text: '...البحث في الإشعارات',
            controller: searchController,
            heintStyle: TextStyle(color: Colors.grey[500]),
            fillColor: Colors.grey[100],
            prefix: Icon(Icons.search, color: Colors.grey[500]),
            obscureText: false,
          ),
        ),

        // Filter Buttons
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              // Filter by status
              _buildFilterButton('مقروءة', true),
              hSpace(12),
              _buildFilterButton('غير مقروءة', false),
              hSpace(12),
              _buildShowAllButton(),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // Action Buttons
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Expanded(
                child: _buildActionButton(
                  'حذف المقروءة',
                  Icons.delete,
                  const Color.fromARGB(207, 244, 67, 54),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildActionButton(
                  'تعيين الكل كمقروء',
                  Icons.check,
                  const Color.fromARGB(199, 139, 195, 74),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 20),

        // Notifications List
        Expanded(
          child: ListView(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            children: [
              buildNotificationItemFixed(),
              buildNotificationItemNotFixed(
                textHeader: 'عرض خاص',
                time: '22-2-2003',
                textBody: 'ndindiwkmwomsmsowksm',
              ),
              buildNotificationItemFixed(),
              buildNotificationItemFixed(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatItem(String count, String label, Color color) {
    return Column(
      children: [
        Text(
          count,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(label, style: TextStyle(fontSize: 14, color: Colors.grey[600])),
      ],
    );
  }

  Widget _buildFilterButton(String text, bool hasNotification) {
    bool isSelected = selectedStatus == text;
    return GestureDetector(
      onTap: () {
        setState(() {
          selectedStatus = text;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue[50] : Colors.grey[100],
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.grey[300]!,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Text(
                  text,
                  style: TextStyle(
                    color: isSelected ? Colors.blue : Colors.grey[700],
                    fontWeight:
                        isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
                if (hasNotification == false) hSpace(5),
                if (hasNotification == false)
                  Icon(Icons.notifications, color: Colors.grey, size: 16),
                if (hasNotification) hSpace(5),
                if (hasNotification)
                  Icon(Icons.check, color: Colors.grey, size: 20),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShowAllButton() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      decoration: BoxDecoration(
        color: Colors.blueAccent,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'الكل',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
          ),
          hSpace(5),
          Icon(Icons.list, color: Colors.white, size: 18),
        ],
      ),
    );
  }

  Widget _buildActionButton(String text, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 7),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(25),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            text,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(width: 8),

          Icon(icon, color: Colors.white, size: 18),
        ],
      ),
    );
  }
}
