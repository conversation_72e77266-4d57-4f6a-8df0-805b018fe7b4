import 'package:al_7esa/core/spacing.dart';
import 'package:flutter/material.dart';

class InformationPayment extends StatelessWidget {
  final List<Color> gradientColors;

  const InformationPayment({super.key, required this.gradientColors});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.2),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          const Text(
            'تعليمات الدفع',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),

          vSpace(20),

          // Step 1
          _buildPaymentStep(
            1,
            'قم بالتحويل إلى رقم المحفظة الإلكترونية',
            '01033607749 / 01145425207\nباي zezohani777@instapay',
          ),

          vSpace(16),

          // Step 2
          _buildPaymentStep(2, 'خذ لقطة شاشة للتحويل', ''),

          vSpace(16),

          // Step 3
          _buildPaymentStep(3, 'قم بتعبئة النموذج وإرفاق لقطة الشاشة', ''),
        ],
      ),
    );
  }

  Widget _buildPaymentStep(int stepNumber, String title, String description) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
                textAlign: TextAlign.right,
              ),
              if (description.isNotEmpty) ...[
                vSpace(8),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                    height: 1.4,
                  ),
                  textAlign: TextAlign.right,
                ),
              ],
            ],
          ),
        ),
        hSpace(16),
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            gradient: LinearGradient(colors: gradientColors),
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              stepNumber.toString(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
