import 'package:al_7esa/core/my_colors.dart';
import 'package:flutter/material.dart';

PreferredSizeWidget? appBarTeatcherHome() => PreferredSize(
  preferredSize: const Size.fromHeight(80),
  child: Container(
    decoration: const BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [MyColors.blueColor, MyColors.pinkColor],
      ),
    ),
    child: <PERSON><PERSON><PERSON>(
      child: Padding(
        padding: const EdgeInsets.all(15),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            InkWell(
              onTap: () {},
              child: Icon(
                Icons.notifications_outlined,
                color: Colors.white,
                size: 24,
              ),
            ),

            // Title
            const Text(
              'لوحة تحكم المعلم',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),

            InkWell(
              onTap: () {},
              child: Icon(Icons.menu, color: Colors.white, size: 24),
            ),
          ],
        ),
      ),
    ),
  ),
);
