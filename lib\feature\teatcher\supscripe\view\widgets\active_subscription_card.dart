import 'package:al_7esa/core/my_colors.dart';
import 'package:al_7esa/core/spacing.dart';
import 'package:al_7esa/feature/teatcher/supscripe/view/subscription_confirmation_screen.dart';
import 'package:flutter/material.dart';

class ActiveSubscriptionCard extends StatelessWidget {
  const ActiveSubscriptionCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [MyColors.blueColor, MyColors.pinkColor],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.3),
            spreadRadius: 1,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            // Star icon and title
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.star, color: Colors.white, size: 24),
                hSpace(8),
                const Text(
                  'باقة النجم',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),

            vSpace(16),

            // Price
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                const Text(
                  'جنيه',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                hSpace(8),
                const Text(
                  '350.0',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 48,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),

            vSpace(8),

            // Duration
            const Text(
              '30 يوم',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),

            vSpace(24),

            // Features
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  _buildFeatureItem('فصل دراسي', true),
                  _buildFeatureItem('محادثات غير مفعلة', false),
                  _buildFeatureItem('مساعد معلم غير متاح', false),
                  _buildFeatureItem('دعم فني على مدار الساعة', true),
                ],
              ),
            ),

            vSpace(24),

            // Subscribe button
            Container(
              width: double.infinity,
              height: 50,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(25),
              ),
              child: ElevatedButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder:
                          (context) => SubscriptionConfirmationScreen(
                            planTitle: 'باقة النجم',
                            price: '350.0',
                            currency: 'جنيه',
                            duration: '30 يوم',
                            features: const [
                              'فصل دراسي',
                              'محادثات غير مفعلة',
                              'مساعد معلم غير متاح',
                              'دعم فني على مدار الساعة',
                            ],
                            gradientColors: const [
                              MyColors.blueColor,
                              MyColors.pinkColor,
                            ],
                          ),
                    ),
                  );
                },

                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: MyColors.blueColor,
                  shadowColor: Colors.transparent,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25),
                  ),
                ),
                child: const Text(
                  'اشترك الآن',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(String feature, bool isActive) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(
            isActive ? Icons.check_circle : Icons.cancel,
            color:
                isActive ? Colors.white : Colors.white.withValues(alpha: 0.6),
            size: 20,
          ),
          hSpace(12),
          Expanded(
            child: Text(
              feature,
              style: TextStyle(
                fontSize: 14,
                color:
                    isActive
                        ? Colors.white
                        : Colors.white.withValues(alpha: 0.8),
                fontWeight: isActive ? FontWeight.w500 : FontWeight.normal,
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }
}
