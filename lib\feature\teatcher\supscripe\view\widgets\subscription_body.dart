import 'package:al_7esa/core/my_colors.dart';
import 'package:al_7esa/core/spacing.dart';
import 'package:al_7esa/feature/teatcher/supscripe/view/widgets/active_subscription_card.dart';
import 'package:al_7esa/feature/teatcher/supscripe/view/widgets/no_subscription_card.dart';
import 'package:al_7esa/feature/teatcher/supscripe/view/widgets/subscription_plan_card.dart';
import 'package:al_7esa/feature/teatcher/supscripe/view/subscription_confirmation_screen.dart';
import 'package:flutter/material.dart';

class SubscriptionBody extends StatelessWidget {
  const SubscriptionBody({super.key});

  void _navigateToConfirmation(
    BuildContext context, {
    required String planTitle,
    required String price,
    required String currency,
    required String duration,
    required List<String> features,
    required List<Color> gradientColors,
  }) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => SubscriptionConfirmationScreen(
              planTitle: planTitle,
              price: price,
              currency: currency,
              duration: duration,
              features: features,
              gradientColors: gradientColors,
            ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            vSpace(16),
            const NoSubscriptionCard(),

            vSpace(30),

            // Star Plan
            SubscriptionPlanCard(
              title: 'باقة النجم',
              price: '350.0',
              currency: 'جنيه',
              duration: 'ي30',
              features: const [
                'فصل دراسي 1 ',
                'محادثات غير مفعلة',
                'مساعد معلم غير متاح',
                'دعم فني على مدار الساعة',
              ],
              isPopular: false,
              gradientColors: const [MyColors.blueColor, MyColors.pinkColor],
              onSubscribe: () {
                _navigateToConfirmation(
                  context,
                  planTitle: 'باقة النجم',
                  price: '350.0',
                  currency: 'جنيه',
                  duration: '30 يوم',
                  features: const [
                    'فصل دراسي 1',
                    'محادثات غير مفعلة',
                    'مساعد معلم غير متاح',
                    'دعم فني على مدار الساعة',
                  ],
                  gradientColors: const [
                    MyColors.blueColor,
                    MyColors.pinkColor,
                  ],
                );
              },
              iconData: Icon(Icons.star, color: Colors.amber),
            ),

            vSpace(20),

            // Premium Plan
            SubscriptionPlanCard(
              title: 'باقة دماغ عاليه',
              price: '550.0',
              currency: 'جنيه',
              duration: '30 يوم',
              features: const [
                '2 فصول دراسية',
                'محادثات غير مفعلة',
                'مساعد معلم متاح',
                'دعم فني على مدار الساعة',
              ],
              isPopular: false,
              gradientColors: const [Colors.purple, Colors.deepPurple],
              onSubscribe: () {
                _navigateToConfirmation(
                  context,
                  planTitle: 'باقة دماغ عاليه',
                  price: '550.0',
                  currency: 'جنيه',
                  duration: '30 يوم',
                  features: const [
                    '2 فصول دراسية',
                    'محادثات غير مفعلة',
                    'مساعد معلم متاح',
                    'دعم فني على مدار الساعة',
                  ],
                  gradientColors: const [Colors.purple, Colors.deepPurple],
                );
              },
              iconData: Text('🧠', style: TextStyle(fontSize: 20)),
            ),

            vSpace(20),

            // Pro Plan
            SubscriptionPlanCard(
              title: 'vip باقة مستر',
              price: '850.0',
              currency: 'جنيه',
              duration: '30 يوم',
              features: const [
                '5 فصل دراسي',
                'محادثات غير مفعلة',
                'مساعد معلم متاح',
                'دعم فني على مدار الساعة',
              ],
              isPopular: false,
              gradientColors: const [Colors.green, Colors.teal],
              onSubscribe: () {
                _navigateToConfirmation(
                  context,
                  planTitle: 'vip باقة مستر',
                  price: '850.0',
                  currency: 'جنيه',
                  duration: '30 يوم',
                  features: const [
                    '5 فصل دراسي',
                    'محادثات غير مفعلة',
                    'مساعد معلم متاح',
                    'دعم فني على مدار الساعة',
                  ],
                  gradientColors: const [Colors.green, Colors.teal],
                );
              },
              iconData: Text('👑', style: TextStyle(fontSize: 20)),
            ),

            vSpace(40),
            SubscriptionPlanCard(
              title: 'باقه تجريبيه',
              price: '0.0',
              currency: 'جنيه',
              duration: '1 يوم',
              features: const [
                '2 فصل دراسي',
                'محادثات مفعلة',
                'مساعد معلم غير متاح',
                'دعم فني على مدار الساعة',
              ],
              isPopular: false,
              gradientColors: const [Colors.deepPurple, Colors.purpleAccent],
              onSubscribe: () {
                _navigateToConfirmation(
                  context,
                  planTitle: 'باقه تجريبيه',
                  price: '0.0',
                  currency: 'جنيه',
                  duration: '1 يوم',
                  features: const [
                    '2 فصل دراسي',
                    'محادثات مفعلة',
                    'مساعد معلم غير متاح',
                    'دعم فني على مدار الساعة',
                  ],
                  gradientColors: const [Colors.green, Colors.teal],
                );
              },
              iconData: Text('', style: TextStyle(fontSize: 20)),
            ),

            vSpace(40),
          ],
        ),
      ),
    );
  }
}
