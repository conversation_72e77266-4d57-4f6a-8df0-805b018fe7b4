import 'package:al_7esa/core/spacing.dart';
import 'package:flutter/material.dart';

class UploadImage extends StatelessWidget {
  const UploadImage({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        _showImagePicker(context);
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.2),
              spreadRadius: 1,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            // Upload icon and text
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.cloud_upload,
                color: Colors.blue,
                size: 40,
              ),
            ),

            vSpace(16),

            const Text(
              'اضغط هنا لرفع لقطة الشاشة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),

            vSpace(8),

            Text(
              'يجب أن تكون الصورة واضحة وتظهر تفاصيل التحويل',
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _showImagePicker(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'اختر طريقة رفع الصورة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                vSpace(20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildImageOption(
                      context,
                      Icons.camera_alt,
                      'الكاميرا',
                      () {
                        Navigator.pop(context);
                        _pickImageFromCamera(context);
                      },
                    ),
                    _buildImageOption(
                      context,
                      Icons.photo_library,
                      'المعرض',
                      () {
                        Navigator.pop(context);
                        _pickImageFromGallery(context);
                      },
                    ),
                  ],
                ),
                vSpace(20),
              ],
            ),
          ),
    );
  }

  Widget _buildImageOption(
    BuildContext context,
    IconData icon,
    String title,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(icon, size: 40, color: Colors.blue),
            vSpace(8),
            Text(
              title,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
          ],
        ),
      ),
    );
  }

  void _pickImageFromCamera(BuildContext context) {
    // Handle camera image picking
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم فتح الكاميرا'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _pickImageFromGallery(BuildContext context) {
    // Handle gallery image picking
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم فتح المعرض'),
        backgroundColor: Colors.blue,
      ),
    );
  }
}
