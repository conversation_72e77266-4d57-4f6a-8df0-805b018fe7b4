import 'package:flutter/material.dart';

Widget buildNotificationItemFixed() {
  return Container(
    margin: const EdgeInsets.only(bottom: 16),
    padding: const EdgeInsets.all(16),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(12),
      boxShadow: [
        BoxShadow(
          color: Colors.grey.withOpacity(0.1),
          spreadRadius: 1,
          blurRadius: 4,
          offset: const Offset(0, 2),
        ),
      ],
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        // Header with emoji and title
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '07:30 07-29',
              style: TextStyle(fontSize: 12, color: Colors.grey[500]),
            ),

            // Title with emoji
            const Row(
              children: [
                Text('🎉', style: TextStyle(fontSize: 20)),
                SizedBox(width: 8),
                Text(
                  'مرحباً بك في منصة الحصة',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ],
        ),

        const SizedBox(height: 12),

        // Notification content
        Text(
          'أهلاً وسهلاً بك Abdel-Rahman Kamal نحن سعداء لانضمامك كمعلم في منصة الحصة. يمكنك الآن إنشاء فصولك الدراسية وبدء رحلة التعليم الرقمي. تم منحك باقة تجريبية مجانية لمدة يوم واحد للاستمتاع بجميع المميزات.',
          style: TextStyle(fontSize: 14, color: Colors.grey[700], height: 1.5),
          textAlign: TextAlign.right,
        ),

        const SizedBox(height: 12),

        // Date
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: const Color.fromARGB(208, 244, 67, 54),
            borderRadius: BorderRadius.circular(15),
          ),
          child: const Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'حذف',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(width: 4),
              Icon(Icons.delete, color: Colors.white, size: 16),
            ],
          ),
        ),
      ],
    ),
  );
}
