import 'package:al_7esa/core/my_text_field.dart';
import 'package:al_7esa/core/spacing.dart';
import 'package:flutter/material.dart';

class ChangePassword extends StatelessWidget {
  final TextEditingController currentPasswordController;
  final TextEditingController newPasswordController;
  final TextEditingController confirmPasswordController;
  const ChangePassword({
    super.key,
    required this.currentPasswordController,
    required this.newPasswordController,
    required this.confirmPasswordController,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.2),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              const Text(
                'تغيير كلمة المرور',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              hSpace(8),
              const Icon(Icons.lock, color: Colors.grey, size: 20),
            ],
          ),
          vSpace(16),

          // Current Password
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              const Text(
                'كلمة المرور الحالية',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
              hSpace(8),
              const Icon(Icons.lock_outline, color: Colors.grey, size: 20),
            ],
          ),
          vSpace(8),
          CustomTextField(
            controller: currentPasswordController,
            text: '',
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'يرجى إدخال كلمة المرور الحالية';
              }
              return null;
            },
            onTap: () {},
            obscureText: true,
            fillColor: Colors.grey[100],
          ),

          vSpace(16),

          // New Password
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              const Text(
                'كلمة المرور الجديدة',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
              hSpace(8),
              const Icon(Icons.vpn_key, color: Colors.grey, size: 20),
            ],
          ),

          vSpace(8),
          CustomTextField(
            controller: newPasswordController,
            text: '',
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'يرجى إدخال كلمة المرور الجديدة';
              }
              if (value.length < 6) {
                return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
              }
              return null;
            },
            onTap: () {},
            obscureText: true,
            fillColor: Colors.grey[100],
          ),

          vSpace(10),
          Align(
            alignment: Alignment.centerRight,
            child: const Text(
              'يجب أن تكون 8 أحرف على الأقل',
              textAlign: TextAlign.end,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
          vSpace(16),

          // Confirm New Password
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              const Text(
                'تأكيد كلمة المرور الجديدة',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
              hSpace(8),
              const Icon(
                Icons.check_circle_outline,
                color: Colors.grey,
                size: 20,
              ),
            ],
          ),
          vSpace(8),
          CustomTextField(
            controller: confirmPasswordController,
            text: '',
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'يرجى تأكيد كلمة المرور الجديدة';
              }
              if (value != newPasswordController.text) {
                return 'كلمة المرور غير متطابقة';
              }
              return null;
            },
            onTap: () {},
            obscureText: true,
            fillColor: Colors.grey[100],
          ),

          vSpace(20),

          // Change Password Button
          Row(
            children: [
              Expanded(flex: 2, child: hSpace(10)),

              Expanded(
                flex: 2,
                child: Container(
                  width: double.infinity,
                  height: 50,
                  decoration: BoxDecoration(
                    color: const Color(0xFFFFC107),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: ElevatedButton(
                    onPressed: () {
                      // Handle change password
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.transparent,
                      shadowColor: Colors.transparent,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'تغيير كلمة المرور',
                          style: TextStyle(
                            color: Colors.black87,
                            fontSize: 13,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        hSpace(5),
                        Icon(Icons.lock, color: Colors.black87, size: 15),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
