import 'package:al_7esa/core/my_colors.dart';
import 'package:al_7esa/core/spacing.dart';
import 'package:flutter/material.dart';

PreferredSizeWidget? appBarTeatcherNotification() => PreferredSize(
  preferredSize: const Size.fromHeight(80),
  child: Container(
    decoration: const BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [MyColors.blueColor, MyColors.pinkColor],
      ),
    ),
    child: SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            InkWell(
              onTap: () {},
              child: Icon(
                Icons.notifications_outlined,
                color: Colors.white,
                size: 24,
              ),
            ),

            // Title
            Text(
              'الإشعارات',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            hSpace(0),
          ],
        ),
      ),
    ),
  ),
);
