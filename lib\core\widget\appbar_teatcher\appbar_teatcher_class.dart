import 'package:al_7esa/core/my_colors.dart';
import 'package:flutter/material.dart';

PreferredSizeWidget? appBarTeatcherClasses() => PreferredSize(
  preferredSize: const Size.fromHeight(80),
  child: Container(
    decoration: const BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [MyColors.blueColor, MyColors.pinkColor],
      ),
    ),
    child: <PERSON><PERSON><PERSON>(
      child: Padding(
        padding: const EdgeInsets.all(15),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Title
            Padding(
              padding: const EdgeInsets.only(right: 10),
              child: Text(
                'الفصول الدراسية',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    ),
  ),
);
