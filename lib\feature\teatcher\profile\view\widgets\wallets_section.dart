import 'package:al_7esa/core/my_text_field.dart';
import 'package:al_7esa/core/spacing.dart';
import 'package:flutter/material.dart';

class WalletsSection extends StatelessWidget {
  final TextEditingController walletController;
  final TextEditingController secondWalletController;
  const WalletsSection({
    super.key,
    required this.walletController,
    required this.secondWalletController,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: const Color(0xFFE0F7FA), // Light cyan background
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Expanded(
                    child: Text(
                      'أرقام المحافظ الإلكترونية: يمكنك إضافة رقمين للمحفظة الإلكترونية لاستقبال المدفوعات',
                      textAlign: TextAlign.right,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF00695C),
                      ),
                    ),
                  ),
                  hSpace(12),

                  const Icon(
                    Icons.account_balance_wallet,
                    color: Colors.teal,
                    size: 20,
                  ),
                ],
              ),
            ],
          ),
        ),

        vSpace(24),

        // First Wallet Number
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.2),
                spreadRadius: 1,
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // First wallet field
              Row(
                mainAxisAlignment: MainAxisAlignment.end,

                children: [
                  const Text(
                    'رقم المحفظة الأول',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                  ),
                  hSpace(8),
                  const Icon(
                    Icons.account_balance_wallet,
                    color: Colors.grey,
                    size: 20,
                  ),
                ],
              ),
              vSpace(12),
              CustomTextField(
                controller: walletController,
                text: 'رقم المحفظة الأول',
                validator: (value) {
                  return null; // Optional field
                },
                onTap: () {},
                obscureText: false,
                fillColor: Colors.grey[100],
              ),

              vSpace(12),

              Align(
                alignment: Alignment.centerRight,
                child: const Text(
                  'مثال: ***********',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ],
          ),
        ),

        vSpace(24),

        // Second Wallet Number
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.2),
                spreadRadius: 1,
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Second wallet field
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  const Text(
                    'رقم المحفظة الثاني (اختياري)',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                  ),
                  hSpace(8),

                  const Icon(
                    Icons.account_balance_wallet,
                    color: Colors.grey,
                    size: 20,
                  ),
                ],
              ),
              vSpace(12),
              CustomTextField(
                controller: secondWalletController,
                text: 'رقم المحفظة الثاني',
                validator: (value) {
                  return null; // Optional field
                },
                onTap: () {},
                obscureText: false,
                fillColor: Colors.grey[100],
              ),

              vSpace(12),

              Align(
                alignment: Alignment.centerRight,
                child: const Text(
                  'رقم بديل للمحفظة الإلكترونية',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
