import 'package:al_7esa/core/my_colors.dart';
import 'package:al_7esa/core/spacing.dart';
import 'package:al_7esa/feature/teatcher/supscripe/view/widgets/information_payment.dart';
import 'package:al_7esa/feature/teatcher/supscripe/view/widgets/upload_image.dart';
import 'package:flutter/material.dart';

class SubscriptionConfirmationBody extends StatelessWidget {
  final String planTitle;
  final String price;
  final String currency;
  final String duration;
  final List<String> features;
  final List<Color> gradientColors;

  const SubscriptionConfirmationBody({
    super.key,
    required this.planTitle,
    required this.price,
    required this.currency,
    required this.duration,
    required this.features,
    required this.gradientColors,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            vSpace(20),

            // Plan Card
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.2),
                    spreadRadius: 1,
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Column(
                        mainAxisAlignment: MainAxisAlignment.end,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            planTitle,
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Text(
                                currency,
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.black87,
                                ),
                              ),

                              Text(
                                price,
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black54,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      hSpace(10),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(colors: gradientColors),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child:
                            planTitle == 'باقة دماغ عاليه'
                                ? Text('🧠', style: TextStyle(fontSize: 20))
                                : planTitle == 'vip باقة مستر'
                                ? Text('👑', style: TextStyle(fontSize: 20))
                                : Icon(Icons.star, color: Colors.white),
                      ),
                    ],
                  ),
                  vSpace(10),

                  dSpace(),

                  // Price
                  vSpace(10),

                  // Duration and classroom info
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      _buildInfoItem(Icons.access_time, duration),

                      hSpace(50),
                      _buildInfoItem(
                        Icons.class_,
                        planTitle == 'باقة دماغ عاليه'
                            ? '2 فصل'
                            : planTitle == 'vip باقة مستر'
                            ? '5 فصل'
                            : planTitle == 'باقه تجريبيه'
                            ? '2 فصل'
                            : '1 فصل',
                      ),
                    ],
                  ),
                ],
              ),
            ),

            vSpace(30),

            // Information Payment
            InformationPayment(gradientColors: gradientColors),

            vSpace(30),

            // Upload Screenshot Section
            UploadImage(),
            vSpace(20),

            // Transfer Notes Section
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),

              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  const Text(
                    'ملاحظات التحويل (اختياري)',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black54,
                    ),
                  ),

                  vSpace(16),

                  Container(
                    width: double.infinity,
                    height: 60,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                    child: TextField(
                      textAlign: TextAlign.right,
                      decoration: InputDecoration(
                        hintText: 'مثال: رقم الجوالة',
                        hintStyle: TextStyle(
                          color: Colors.grey[500],
                          fontSize: 14,
                        ),
                        border: InputBorder.none,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            vSpace(30),

            // Confirm Payment Button
            Container(
              width: double.infinity,
              height: 56,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [MyColors.blueColor, MyColors.pinkColor],
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                ),
                borderRadius: BorderRadius.circular(28),
              ),
              child: ElevatedButton(
                onPressed: () {},
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.transparent,
                  shadowColor: Colors.transparent,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(28),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text(
                      'تأكيد الدفع',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    hSpace(8),
                    const Icon(Icons.check, color: Colors.white, size: 20),
                  ],
                ),
              ),
            ),
            vSpace(40),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 20, color: Colors.grey[600]),
        hSpace(8),
        Text(text, style: TextStyle(fontSize: 14, color: Colors.grey[600])),
      ],
    );
  }
}
