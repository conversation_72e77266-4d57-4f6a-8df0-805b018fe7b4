import 'package:al_7esa/core/spacing.dart';
import 'package:al_7esa/feature/teatcher/profile/view/widgets/change_password.dart';
import 'package:al_7esa/feature/teatcher/profile/view/widgets/dialog_signout.dart';
import 'package:al_7esa/feature/teatcher/profile/view/widgets/header_profile.dart';
import 'package:al_7esa/feature/teatcher/profile/view/widgets/information_profile.dart';
import 'package:al_7esa/feature/teatcher/profile/view/widgets/wallets_section.dart';
import 'package:flutter/material.dart';

class ProfileBody extends StatefulWidget {
  const ProfileBody({super.key});

  @override
  State<ProfileBody> createState() => _ProfileBodyState();
}

class _ProfileBodyState extends State<ProfileBody> {
  final TextEditingController nameController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController alternativePhoneController =
      TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController walletController = TextEditingController();
  final TextEditingController secondWalletController = TextEditingController();
  final TextEditingController currentPasswordController =
      TextEditingController();
  final TextEditingController newPasswordController = TextEditingController();
  final TextEditingController confirmPasswordController =
      TextEditingController();

  @override
  void initState() {
    super.initState();
    // Initialize with default values
    nameController.text = 'Abdel-Rahman Kamal';
    phoneController.text = '01207347771';
    emailController.text = '5abdo <EMAIL>';
    walletController.text = '01xxxxxxxxx';
    secondWalletController.text = '01xxxxxxxxx';
  }

  @override
  void dispose() {
    nameController.dispose();
    phoneController.dispose();
    alternativePhoneController.dispose();
    emailController.dispose();
    walletController.dispose();
    secondWalletController.dispose();
    currentPasswordController.dispose();
    newPasswordController.dispose();
    confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          // Profile content
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                // Profile card
                HeaderProfile(),

                vSpace(24),

                // Form fields
                InformationProfile(
                  nameController: nameController,
                  phoneController: phoneController,
                  alternativePhoneController: alternativePhoneController,
                  emailController: emailController,
                ),

                vSpace(24),

                // Electronic Wallets Section
                WalletsSection(
                  walletController: walletController,
                  secondWalletController: secondWalletController,
                ),
                vSpace(24),

                // Profile Picture Section
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withValues(alpha: 0.2),
                        spreadRadius: 1,
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          const Text(
                            'صورة الملف الشخصي',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.black87,
                            ),
                          ),
                          hSpace(8),
                          const Icon(
                            Icons.camera_alt,
                            color: Colors.grey,
                            size: 20,
                          ),
                        ],
                      ),
                      vSpace(16),
                      Row(
                        children: [
                          Expanded(
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              decoration: BoxDecoration(
                                color: Colors.grey[100],
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.grey[300]!),
                              ),
                              child: const Text(
                                'لم يتم اختيار أي ملف',
                                style: TextStyle(
                                  color: Colors.grey,
                                  fontSize: 14,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ),
                          hSpace(12),
                          Expanded(
                            child: OutlinedButton(
                              onPressed: () {
                                // Handle file selection
                              },
                              style: OutlinedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 16,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: const Text(
                                'اختيار ملف',
                                style: TextStyle(
                                  color: Colors.black87,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                vSpace(24),

                // Save Changes Button
                Container(
                  width: double.infinity,
                  height: 50,
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                      colors: [Color(0xFF6366F1), Color(0xFFEC4899)],
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: ElevatedButton(
                    onPressed: () {
                      // Handle save changes
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.transparent,
                      shadowColor: Colors.transparent,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'حفظ التغييرات',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        SizedBox(width: 8),

                        Icon(Icons.save, color: Colors.white, size: 20),
                      ],
                    ),
                  ),
                ),

                vSpace(24),
                // Change Password Section
                ChangePassword(
                  currentPasswordController: currentPasswordController,
                  newPasswordController: newPasswordController,
                  confirmPasswordController: confirmPasswordController,
                ),

                vSpace(24),

                // Logout Button
                Container(
                  width: double.infinity,
                  height: 50,
                  decoration: BoxDecoration(
                    color: const Color(0xFFFF5722),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: ElevatedButton(
                    onPressed: () {
                      // Handle logout
                      showLogoutDialog(context);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.transparent,
                      shadowColor: Colors.transparent,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'تسجيل الخروج',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        SizedBox(width: 8),
                        Icon(Icons.logout, color: Colors.white, size: 20),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
