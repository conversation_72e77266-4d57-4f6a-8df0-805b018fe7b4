import 'package:al_7esa/core/my_text_field.dart';
import 'package:al_7esa/core/spacing.dart';
import 'package:flutter/material.dart';

class InformationProfile extends StatelessWidget {
  final TextEditingController nameController;
  final TextEditingController phoneController;
  final TextEditingController alternativePhoneController;
  final TextEditingController emailController;
  const InformationProfile({
    super.key,
    required this.nameController,
    required this.phoneController,
    required this.alternativePhoneController,
    required this.emailController,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.2),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Full name field
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              const Text(
                'الاسم الكامل',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
              hSpace(8),
              const Icon(Icons.person, color: Colors.grey, size: 20),
            ],
          ),
          vSpace(12),
          CustomTextField(
            controller: nameController,
            text: 'الاسم الكامل',
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'يرجى إدخال الاسم الكامل';
              }
              return null;
            },
            onTap: () {},
            obscureText: false,
            fillColor: Colors.grey[100],
          ),

          vSpace(20),

          // Phone number field
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              const Text(
                'رقم الهاتف',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
              hSpace(8),
              const Icon(Icons.phone, color: Colors.grey, size: 20),
            ],
          ),
          vSpace(12),
          CustomTextField(
            controller: phoneController,
            text: 'رقم الهاتف',
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'يرجى إدخال رقم الهاتف';
              }
              return null;
            },
            onTap: () {},
            obscureText: false,
            fillColor: Colors.grey[100],
          ),

          vSpace(12),

          Align(
            alignment: Alignment.centerRight,
            child: const Text(
              'لا يمكن تغيير رقم الهاتف الأساسي',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),

          vSpace(20),

          // Alternative phone number field
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              const Text(
                'رقم هاتف بديل (اختياري)',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
              hSpace(8),
              const Icon(Icons.phone_android, color: Colors.grey, size: 20),
            ],
          ),
          vSpace(12),
          CustomTextField(
            controller: alternativePhoneController,
            text: '',
            validator: (value) {
              return null; // Optional field
            },
            onTap: () {},
            obscureText: false,
            fillColor: Colors.grey[100],
          ),

          vSpace(20),

          // Email field
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              const Text(
                'البريد الإلكتروني (اختياري)',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
              hSpace(8),
              const Icon(Icons.email, color: Colors.grey, size: 20),
            ],
          ),
          vSpace(12),
          CustomTextField(
            controller: emailController,
            text: 'البريد الإلكتروني',
            validator: (value) {
              return null; // Optional field
            },
            onTap: () {},
            obscureText: false,
            fillColor: Colors.grey[100],
          ),
        ],
      ),
    );
  }
}
