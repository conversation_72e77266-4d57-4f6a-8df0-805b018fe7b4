import 'package:al_7esa/core/my_colors.dart';
import 'package:flutter/material.dart';

PreferredSizeWidget? appBarTeatcherSubscriptionConfirmation() => PreferredSize(
  preferredSize: const Size.fromHeight(80),
  child: Container(
    decoration: const BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [MyColors.blueColor, MyColors.pinkColor],
      ),
    ),
    child: Safe<PERSON><PERSON>(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Back button
            Builder(
              builder:
                  (context) => IconButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    icon: const Icon(
                      Icons.arrow_forward,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
            ),

            // Title
            const Text(
              'تأكيد الدفع',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),

            // Empty space for symmetry
            const SizedBox(width: 48),
          ],
        ),
      ),
    ),
  ),
);
