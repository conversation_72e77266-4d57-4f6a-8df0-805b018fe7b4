import 'package:al_7esa/core/widget/appbar_student/appbar_atudent_home.dart';
import 'package:al_7esa/feature/student/class/view/class_screen_student.dart';
import 'package:al_7esa/feature/student/home/<USER>/home_screen_student_body.dart';
import 'package:flutter/material.dart';

class StudentHomeScreen extends StatefulWidget {
  const StudentHomeScreen({super.key});

  @override
  State<StudentHomeScreen> createState() => _StudentHomeScreenState();
}

class _StudentHomeScreenState extends State<StudentHomeScreen> {
  int _currentIndex = 0;

  final List<Widget> _screens = [
    HomeScreenStudentBody(),
    HomeScreenStudentBody(),
    ClassScreenStudent(),
    ClassScreenStudent(),
  ];
  final List<PreferredSizeWidget?> appBarWidget = [
    appBarStudentHome(),
    appBarStudentHome(),
    appBarStudentHome(),
    appBarStudentHome(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBarWidget[_currentIndex],
      body: _screens[_currentIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        backgroundColor: Colors.white,
        selectedItemColor: const Color.fromARGB(255, 74, 70, 193),
        unselectedItemColor: Colors.grey[600],
        selectedLabelStyle: const TextStyle(fontSize: 12),
        unselectedLabelStyle: const TextStyle(fontSize: 12),
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'الرئيسية'),
          BottomNavigationBarItem(icon: Icon(Icons.class_), label: 'الفصول'),
          BottomNavigationBarItem(
            icon: Icon(Icons.notifications),
            label: 'الإشعارات',
          ),

          BottomNavigationBarItem(icon: Icon(Icons.person), label: 'حسابي'),
        ],
      ),
    );
  }
}

// Placeholder screens for each tab
